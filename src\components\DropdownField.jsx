import React from 'react';

/**
 * 下拉选择组件
 * @param {string} id - 下拉框ID
 * @param {string} placeholder - 占位符文本
 * @param {string} selectedValue - 当前选中值
 * @param {string} selectedText - 当前选中文本
 * @param {boolean} isOpen - 是否打开下拉框
 * @param {boolean} isLoading - 是否加载中
 * @param {boolean} disabled - 是否禁用
 * @param {boolean} error - 是否有错误
 * @param {boolean} clearable - 是否可清空
 * @param {Array} options - 选项数组
 * @param {Function} onToggle - 切换下拉框状态
 * @param {Function} onSelect - 选择选项回调
 * @param {Function} onClear - 清空选项回调
 * @param {string} valueKey - 选项值的键名，默认为 'id'
 * @param {string} textKey - 选项文本的键名，默认为 'name'
 */
export const DropdownField = ({
  id,
  placeholder = '请选择',
  selectedValue,
  selectedText,
  isOpen = false,
  isLoading = false,
  disabled = false,
  error = false,
  clearable = false,
  options = [],
  onToggle,
  onSelect,
  onClear,
  valueKey = 'id',
  textKey = 'name'
}) => {
  const handleOptionClick = (option) => {
    onSelect && onSelect(option);
  };

  const handleClear = (e) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发下拉框打开
    onClear && onClear();
  };

  return (
    <div className="relative" id={id}>
      <div
        onClick={() => !isLoading && !disabled && onToggle && onToggle()}
        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
          error ? 'border-red-500' : 'border-gray-300 hover:border-blue-500'
        } ${isLoading || disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <span className={selectedValue && selectedText ? 'text-gray-900' : 'text-gray-400'}>
          {isLoading ? '加载中...' : (selectedText || placeholder)}
        </span>
        <div className="flex items-center gap-1">
          {/* 清空按钮 - 只在有选中值且可清空时显示 */}
          {clearable && selectedValue && selectedText && !isLoading && !disabled && (
            <button
              onClick={handleClear}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
              type="button"
            >
              <svg className="h-4 w-4 text-gray-400 hover:text-gray-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}

          {/* 下拉箭头或加载图标 */}
          {isLoading ? (
            <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${isOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          )}
        </div>
      </div>

      {isOpen && !isLoading && !disabled && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="py-1 max-h-60 overflow-auto">
            {options.map(option => (
              <div
                key={option[valueKey]}
                onClick={() => handleOptionClick(option)}
                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                  selectedValue === option[valueKey].toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                }`}
              >
                {option[textKey]}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
