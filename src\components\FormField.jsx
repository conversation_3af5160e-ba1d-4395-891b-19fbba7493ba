import React from 'react';

/**
 * 错误提示组件
 */
const ErrorMessage = ({ message }) => (
  <div className="text-red-500 text-sm mt-1">{message}</div>
);

/**
 * 通用表单字段组件
 * @param {string} label - 字段标签
 * @param {boolean} required - 是否必填
 * @param {boolean} error - 是否有错误
 * @param {string} errorMessage - 错误消息
 * @param {React.ReactNode} children - 子组件
 */
export const FormField = ({ label, required = false, error = false, errorMessage, children }) => {
  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {children}
      {error && errorMessage && <ErrorMessage message={errorMessage} />}
    </div>
  );
};

/**
 * 文本输入框组件
 */
export const TextInput = ({ value, onChange, placeholder, error = false, ...props }) => {
  return (
    <input
      type="text"
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
        error ? 'border-red-500' : 'border-gray-300'
      }`}
      {...props}
    />
  );
};

/**
 * 文本域组件
 */
export const TextArea = ({ value, onChange, rows = 4, ...props }) => {
  return (
    <textarea
      value={value}
      onChange={onChange}
      rows={rows}
      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      {...props}
    />
  );
};

/**
 * 日期输入框组件
 */
export const DateInput = ({ value, onChange, ...props }) => {
  return (
    <input
      type="date"
      value={value}
      onChange={onChange}
      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      {...props}
    />
  );
};
