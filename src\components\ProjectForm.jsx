import React from 'react';
import { FormField, TextInput, TextArea, DateInput } from './FormField';
import { DropdownField } from './DropdownField';

/**
 * 项目表单组件
 * @param {Object} formData - 表单数据
 * @param {Object} errors - 表单错误状态
 * @param {Object} dropdownStates - 下拉框状态
 * @param {Object} loadingStates - 加载状态
 * @param {Array} clients - 客户列表
 * @param {Array} managers - 员工列表
 * @param {Array} projectTypes - 项目类型列表
 * @param {Array} priorityOptions - 优先级选项
 * @param {Array} projectStatuses - 项目状态列表
 * @param {boolean} editMode - 是否编辑模式
 * @param {Function} onFormDataChange - 表单数据变更回调
 * @param {Function} onErrorChange - 错误状态变更回调
 * @param {Function} onDropdownToggle - 下拉框切换回调
 * @param {Object} handlers - 各种处理函数
 */
export const ProjectForm = ({
  formData,
  errors,
  dropdownStates,
  loadingStates,
  clients,
  managers,
  projectTypes,
  priorityOptions,
  projectStatuses,
  editMode,
  onFormDataChange,
  onErrorChange,
  onDropdownToggle,
  handlers
}) => {
  const handleInputChange = (field, value) => {
    onFormDataChange(prev => ({ ...prev, [field]: value }));
    onErrorChange(prev => ({ ...prev, [field]: false }));
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        {/* 客户名称 */}
        <FormField
          label="客户名称"
          required
          error={errors.clientId}
          errorMessage="请选择客户名称"
        >
          <DropdownField
            id="client-dropdown"
            placeholder="请选择客户名称"
            selectedValue={formData.clientId}
            selectedText={formData.clientName}
            isOpen={dropdownStates.client}
            isLoading={loadingStates.clients}
            disabled={editMode}
            error={errors.clientId}
            options={clients}
            onToggle={() => onDropdownToggle('client')}
            onSelect={handlers.handleClientChange}
            textKey="company"
          />
        </FormField>

        {/* 项目名称 */}
        <FormField
          label="项目名称"
          required
          error={errors.name}
          errorMessage="请输入项目名称"
        >
          <TextInput
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="请输入项目名称"
            error={errors.name}
          />
        </FormField>

        {/* 项目类型 */}
        <FormField
          label="项目类型"
          required
          error={errors.type}
          errorMessage="请选择项目类型"
        >
          <DropdownField
            id="type-dropdown"
            placeholder="请选择类型"
            selectedValue={formData.type}
            selectedText={projectTypes.find(t => t.id.toString() === formData.type)?.name}
            isOpen={dropdownStates.type}
            error={errors.type}
            options={projectTypes}
            onToggle={() => onDropdownToggle('type')}
            onSelect={handlers.handleTypeChange}
          />
        </FormField>

        {/* 项目负责人 */}
        <FormField
          label="项目负责人"
          required
          error={errors.manager}
          errorMessage="请选择项目负责人"
        >
          <DropdownField
            id="manager-dropdown"
            placeholder="请选择项目负责人"
            selectedValue={formData.manager}
            selectedText={formData.managerName}
            isOpen={dropdownStates.manager}
            isLoading={loadingStates.managers}
            error={errors.manager}
            options={managers}
            onToggle={() => onDropdownToggle('manager')}
            onSelect={handlers.handleManagerChange}
          />
        </FormField>

        {/* 优先级 */}
        <FormField
          label="优先级"
          required
          error={errors.priority}
          errorMessage="请选择项目优先级"
        >
          <DropdownField
            id="priority-dropdown"
            placeholder="请选择优先级"
            selectedValue={formData.priority}
            selectedText={priorityOptions.find(p => p.id === formData.priority)?.name}
            isOpen={dropdownStates.priority}
            error={errors.priority}
            options={priorityOptions}
            onToggle={() => onDropdownToggle('priority')}
            onSelect={handlers.handlePriorityChange}
          />
        </FormField>

        {/* 项目状态 */}
        <FormField
          label="项目状态"
          required
          error={errors.status}
          errorMessage="请选择项目状态"
        >
          <DropdownField
            id="status-dropdown"
            placeholder="请选择状态"
            selectedValue={formData.status}
            selectedText={projectStatuses.find(s => s.id.toString() === formData.status)?.name}
            isOpen={dropdownStates.status}
            error={errors.status}
            options={projectStatuses}
            onToggle={() => onDropdownToggle('status')}
            onSelect={handlers.handleStatusChange}
          />
        </FormField>

        {/* 创建时间 */}
        <FormField
          label="创建时间"
          required
        >
          <DateInput
            value={formData.createTime}
            onChange={(e) => handleInputChange('createTime', e.target.value)}
          />
        </FormField>

        {/* 项目描述 */}
        <div className="col-span-2">
          <FormField label="项目描述">
            <TextArea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
            />
          </FormField>
        </div>
      </div>
    </div>
  );
};
