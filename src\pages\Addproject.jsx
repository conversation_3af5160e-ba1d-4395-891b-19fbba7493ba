import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { projectApi, clientApi, employeeApi } from '../services/projectService';
import { ProjectForm } from '../components/ProjectForm';
import { ErrorDialog } from '../components/ErrorDialog';

// 项目类型配置
const projectTypes = [
  { id: 0, name: '软件开发' },
  { id: 1, name: '市场调研' },
  { id: 2, name: '产品设计' },
  { id: 3, name: '服务项目' }
];

// 项目状态配置
const projectStatuses = [
  { id: 0, name: '未开始' },
  { id: 1, name: '进行中' },
  { id: 2, name: '结项' }
];

// 优先级配置
const priorityOptions = [
  { id: 'high', name: '高优先级' },
  { id: 'medium', name: '中优先级' },
  { id: 'low', name: '低优先级' }
];

/**
 * 项目添加/编辑页面组件
 * 功能：新建项目或编辑现有项目信息
 */
export const Addproject = observer(() => {
  const location = useLocation();
  const navigate = useNavigate();
  const { editMode, projectData } = location.state || {};

  // 数据状态管理
  const [managers, setManagers] = useState([]); // 员工列表
  const [loadingManagers, setLoadingManagers] = useState(false); // 员工加载状态
  const [clients, setClients] = useState([]); // 客户列表
  const [loadingClients, setLoadingClients] = useState(false); // 客户加载状态

  // 错误提示状态
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessages, setErrorMessages] = useState([]);

  // 表单验证错误状态
  const [errors, setErrors] = useState({
    name: false,
    type: false,
    manager: false,
    priority: false,
    clientId: false,
    status: false
  });

  // 下拉框开关状态
  const [dropdownStates, setDropdownStates] = useState({
    client: false,
    type: false,
    manager: false,
    priority: false,
    status: false
  });

  // 优先级转换工具函数
  const getPriorityText = (priority) => {
    const priorityMap = { 0: 'high', 1: 'medium', 2: 'low' };
    return priorityMap[priority] || 'medium';
  };

  const getPriorityNumber = (priority) => {
    const priorityMap = { 'high': 0, 'medium': 1, 'low': 2 };
    return priorityMap[priority] || 1;
  };

  // 统一的下拉框切换函数
  const toggleDropdown = (dropdownName) => {
    setDropdownStates(prev => ({
      ...prev,
      [dropdownName]: !prev[dropdownName]
    }));
  };



  // 表单数据初始化
  const initFormData = () => {
    if (editMode && projectData) {
      return {
        id: projectData.id,
        name: projectData.name || '',
        type: projectData.type?.toString() || '',
        description: projectData.description || '',
        createTime: projectData.createTime ? projectData.createTime.split('T')[0] : new Date().toISOString().split('T')[0],
        priority: getPriorityText(projectData.priority),
        manager: projectData.managerId?.toString() || '',
        managerName: projectData.managerName || '',
        clientId: projectData.clientId || '',
        clientName: projectData.clientName || '',
        status: projectData.status?.toString() || '0'
      };
    }
    return {
      id: null,
      name: '',
      type: '',
      description: '',
      createTime: new Date().toISOString().split('T')[0],
      priority: 'medium',
      manager: '',
      managerName: '',
      clientId: '',
      clientName: '',
      status: '0'
    };
  };

  const [formData, setFormData] = useState(initFormData);

  // 获取员工列表
  const fetchManagers = async () => {
    setLoadingManagers(true);
    try {
      const data = await employeeApi.getEmployeeList();
      setManagers(data || []);
    } catch (error) {
      console.error('获取员工列表出错:', error);
    } finally {
      setLoadingManagers(false);
    }
  };

  // 获取客户列表
  const fetchClients = async () => {
    setLoadingClients(true);
    try {
      const data = await clientApi.getClientList();
      setClients(data || []);
    } catch (error) {
      console.error('获取客户列表出错:', error);
    } finally {
      setLoadingClients(false);
    }
  };

  // 组件初始化：获取数据并设置表单
  useEffect(() => {
    const initData = async () => {
      // 并行获取员工和客户列表
      await Promise.all([fetchManagers(), fetchClients()]);

      // 处理客户信息设置
      if (editMode && projectData) {
        // 编辑模式：使用项目数据中的客户信息
        setFormData(prev => ({
          ...prev,
          clientId: projectData.clientId || '',
          clientName: projectData.clientName || ''
        }));
      } else if (location.state?.client) {
        // 新建模式：使用传入的客户信息
        setFormData(prev => ({
          ...prev,
          clientId: location.state.client.id,
          clientName: location.state.client.company
        }));
      }
    };

    initData();
  }, [editMode, projectData, location.state]);

  // 返回上一页
  const handleReturn = () => {
    window.history.back();
  };

  // 表单验证
  const validateForm = () => {
    const newErrors = {
      name: !formData.name,
      type: !formData.type,
      manager: !formData.manager,
      priority: !formData.priority,
      clientId: !formData.clientId || ['', '0', 'all'].includes(formData.clientId),
      status: !formData.status
    };

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error);
  };

  // 提交表单处理
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const requestData = {
        id: formData.id || 0,
        name: formData.name,
        type: parseInt(formData.type),
        description: formData.description || '',
        createTime: formData.createTime,
        priority: getPriorityNumber(formData.priority),
        managerId: parseInt(formData.manager),
        clientId: parseInt(formData.clientId),
        managerName: formData.managerName || '',
        status: parseInt(formData.status) || 0,
      };

      if (editMode) {
        await projectApi.updateProject(formData.id, requestData);
      } else {
        await projectApi.createProject(requestData);
      }

      navigate(-1);

    } catch (err) {
      console.error('保存项目错误:', err);
      setErrorMessages([err.message]);
      setShowErrorDialog(true);
      setTimeout(() => setShowErrorDialog(false), 2000);
    }
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdownIds = ['client-dropdown', 'type-dropdown', 'manager-dropdown', 'priority-dropdown', 'status-dropdown'];

      dropdownIds.forEach(id => {
        const dropdown = document.getElementById(id);
        if (dropdown && !dropdown.contains(event.target)) {
          const dropdownName = id.replace('-dropdown', '');
          setDropdownStates(prev => ({ ...prev, [dropdownName]: false }));
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 统一的选择处理函数
  const handleSelectChange = (field, value, errorField = field, dropdownName = field) => {
    setFormData(prev => ({ ...prev, ...value }));
    setErrors(prev => ({ ...prev, [errorField]: false }));
    setDropdownStates(prev => ({ ...prev, [dropdownName]: false }));
  };

  // 各类型选择处理函数
  const handleManagerChange = (selectedManager) => {
    handleSelectChange('manager', {
      manager: selectedManager.id.toString(),
      managerName: selectedManager.name
    }, 'manager', 'manager');
  };

  const handleTypeChange = (selectedType) => {
    handleSelectChange('type', {
      type: selectedType.id.toString()
    }, 'type', 'type');
  };

  const handlePriorityChange = (selectedPriority) => {
    handleSelectChange('priority', {
      priority: selectedPriority.id
    }, 'priority', 'priority');
  };

  const handleStatusChange = (selectedStatus) => {
    handleSelectChange('status', {
      status: selectedStatus.id.toString()
    }, 'status', 'status');
  };

  const handleClientChange = (selectedClient) => {
    handleSelectChange('client', {
      clientId: selectedClient.id.toString(),
      clientName: selectedClient.company
    }, 'clientId', 'client');
  };

  // 清空项目负责人
  const handleManagerClear = () => {
    setFormData(prev => ({
      ...prev,
      manager: '',
      managerName: ''
    }));
    setErrors(prev => ({ ...prev, manager: false }));
    setDropdownStates(prev => ({ ...prev, manager: false }));
  };

  return (
    <div className="p-6 pt-16">
      {/* 错误提示对话框 */}
      <ErrorDialog show={showErrorDialog} messages={errorMessages} />

      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">{editMode ? '修改项目' : '新建项目'}</h2>
        </div>

        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6">
            <ProjectForm
              formData={formData}
              errors={errors}
              dropdownStates={dropdownStates}
              loadingStates={{
                clients: loadingClients,
                managers: loadingManagers
              }}
              clients={clients}
              managers={managers}
              projectTypes={projectTypes}
              priorityOptions={priorityOptions}
              projectStatuses={projectStatuses}
              editMode={editMode}
              onFormDataChange={setFormData}
              onErrorChange={setErrors}
              onDropdownToggle={toggleDropdown}
              handlers={{
                handleClientChange,
                handleTypeChange,
                handleManagerChange,
                handleManagerClear,
                handlePriorityChange,
                handleStatusChange
              }}
            />
          </div>

          <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
            <Button variant="outline" onClick={handleReturn}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} >
              {editMode ? '保存修改' : '创建项目'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
});
