import React from 'react';

/**
 * 错误提示对话框组件
 * @param {boolean} show - 是否显示对话框
 * @param {string[]} messages - 错误消息数组
 */
export const ErrorDialog = ({ show, messages }) => {
  if (!show) return null;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="flex items-center gap-2 text-red-500 text-sm bg-red-50 border border-red-200 rounded-md px-3 py-2 w-[400px]">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 shrink-0" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
        {messages.map((error, index) => (
          <span key={index}>{error}</span>
        ))}
      </div>
    </div>
  );
};
