import axios from 'axios';

import { fetchData } from './fetch';


// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (name = '') => {
    const params = new URLSearchParams({ name });
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list?${params}`).then(res => res.json());
  },

  // 获取单个项目详情
  getProjectDetail: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/one/${projectId}`).then(res => res.json()),

  // 创建项目
  createProject: (projectData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    }).then(res => res.json()),

  // 更新项目
  updateProject: (projectId, projectData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/update/${projectId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    }).then(res => res.json()),

  // 删除项目
  deleteProject: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/delete/${projectId}`, {
      method: 'GET'
    }).then(res => res.json()),
};

// 客户相关接口
export const clientApi = {
  // 获取客户列表
  getClientList: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/list`).then(res => res.json()),

  // 搜索客户
  searchClients: (keyword) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/search?keyword=${encodeURIComponent(keyword)}`).then(res => res.json()),

  // 获取单个客户详情
  getClientDetail: (clientId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/one/${clientId}`).then(res => res.json()),

  // 创建客户
  createClient: (clientData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(clientData)
    }).then(res => res.json()),

  // 更新客户
  updateClient: (clientId, clientData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/update/${clientId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(clientData)
    }).then(res => res.json()),

  // 删除客户
  deleteClient: (clientId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/delete/${clientId}`, {
      method: 'GET'
    }).then(res => res.json()),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表 - 修改为与其他服务一致的格式
  getEmployeeList: (organizationId = 0, params = {}) => {
    const queryParams = new URLSearchParams({
      page: 0,
      size: 1000, // 获取所有员工
      ...params
    });
    return fetch(`${fetchData["BASE_URL"]}/api/employees/list/${organizationId}?${queryParams}`)
      .then(res => res.json())
      .then(data => {
        // 如果返回的是分页数据，提取content数组
        if (data && data.content) {
          return data.content;
        }
        // 如果直接返回数组，则直接使用
        return data || [];
      });
  },

  // 获取单个员工详情
  getEmployeeDetail: (employeeId) =>
    fetch(`${fetchData["BASE_URL"]}/api/employees/one/${employeeId}`).then(res => res.json()),
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: async (fileName, bucketName) => {
    try {
      const response = await fetch(`${fetchData["STAFF_URL"]}/api/file/preview?fileName=${encodeURIComponent(fileName)}&bucketName=${bucketName}`);
      
      // 检查响应状态
      if (!response.ok) {
        throw new Error('预览文件失败');
      }

      // 获取响应类型
      const contentType = response.headers.get('content-type');
      
      // 如果是JSON格式
      if (contentType && contentType.includes('application/json')) {
        const text = await response.text();
        try {
          if (!text) {
            throw new Error('空响应');
          }
          const jsonData = JSON.parse(text);
          return jsonData.data || jsonData;
        } catch (e) {
          console.error('JSON解析错误:', e);
          return text;
        }
      }
      
      // 如果是其他格式，直接返回文本
      return response.text();
    } catch (error) {
      console.error('预览文件错误:', error);
      throw new Error(error.message || '预览文件失败');
    }
  },

  // 下载文件
  downloadFile: async (fileName, bucketName) => {
    try {
      const response = await fetch(`${fetchData["PROJECT_URL"]}/api/file/download?fileName=${encodeURIComponent(fileName)}&bucketName=${bucketName}`, {
        headers: {
          'Accept': 'application/octet-stream'
        }
      });
      
      if (!response.ok) {
        throw new Error('下载文件失败');
      }
      
      return response.blob();
    } catch (error) {
      console.error('下载文件错误:', error);
      throw new Error(error.message || '下载文件失败');
    }
  },

  // 全量打包下载
  archiveAll: async (bucketId) => {
    try {
      const response = await fetch(`${fetchData["PROJECT_URL"]}/api/file/archive?bucketId=${bucketId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/octet-stream'
        }
      });
      
      if (!response.ok) {
        throw new Error('打包下载失败');
      }
      
      return response.blob();
    } catch (error) {
      console.error('打包下载错误:', error);
      throw new Error(error.message || '打包下载失败');
    }
  }
};

// 项目输入相关接口
export const projectInputApi = {
  // 获取项目输入列表
  getProjectInputList: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/by-project/${projectId}`).then(res => res.json()),

  // 获取项目输入详情
  getProjectInputDetail: (inputId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/one/${inputId}`).then(res => res.json()),

  // 创建项目输入
  createProjectInput: (formData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新项目输入
  updateProjectInput: (inputId, formData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/update/${inputId}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除项目输入
  deleteProjectInput: (inputId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/delete/${inputId}`, {
      method: 'GET'
    }).then(res => {
      if (!res.ok) {
        throw new Error('删除输入失败');
      }
      return res.text().then(text => {
        if (!text) {
          return { success: true };
        }
        try {
          return JSON.parse(text);
        } catch (e) {
          return { success: true };
        }
      });
    }),

  // 搜索项目输入
  searchProjectInput: (projectId, name) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/by-project/${projectId}?name=${encodeURIComponent(name)}`).then(res => res.json()),

  
  // 打包下载项目输入文件
  archiveProjectInput: (fileId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/archive/one?projectInId=${fileId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/octet-stream'
      }
    }).then(res => res.blob()),
}; 